import apiClient from './apiClient';

const getProductData = (params) => {
  return apiClient.get('/product-data', { params });
};

const getProductDetail = (id) => {
  return apiClient.get(`/product-data/${id}`);
};

const getPriceHistory = (params) => {
  return apiClient.get('/product-data/price-history', { params });
};

const compareProducts = (data) => {
  return apiClient.post('/product-data/compare', data);
};

const deleteProduct = (id) => {
  return apiClient.delete(`/product-data/${id}`);
};

// 产品历史数据相关API
const getProductHistory = (taskId, itemId, params) => {
  return apiClient.get(`/product-data-history/task/${taskId}/item/${itemId}`, { params });
};

const getProductHistoryDetail = (historyId) => {
  return apiClient.get(`/product-data-history/detail/${historyId}`);
};

const getProductHistoryByTime = (taskId, itemId, targetTime) => {
  return apiClient.get(`/product-data-history/task/${taskId}/item/${itemId}/at/${targetTime}`);
};

const getProductTrends = (taskId, itemId, params) => {
  return apiClient.get(`/product-data-history/task/${taskId}/item/${itemId}/trends`, { params });
};

const getProductHistorySummary = (taskId, itemId, params) => {
  return apiClient.get(`/product-data-history/task/${taskId}/item/${itemId}/summary`, { params });
};

const getLatestHistoryForAllProducts = (taskId, params) => {
  return apiClient.get(`/product-data-history/task/${taskId}/items/latest`, { params });
};

export default {
  getProductData,
  getProductDetail,
  getPriceHistory,
  compareProducts,
  deleteProduct,
  // 历史数据方法
  getProductHistory,
  getProductHistoryDetail,
  getProductHistoryByTime,
  getProductTrends,
  getProductHistorySummary,
  getLatestHistoryForAllProducts,
};