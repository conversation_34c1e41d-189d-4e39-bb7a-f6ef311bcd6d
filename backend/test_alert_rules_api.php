<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\AlertRuleController;
use App\Models\AlertRule;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试预警规则API ===\n\n";

// 1. 模拟用户认证
echo "1. 模拟用户认证...\n";
$user = User::first();
if (!$user) {
    echo "   错误: 没有找到用户\n";
    exit(1);
}

Auth::login($user);
echo "   已登录用户: {$user->name} (ID: {$user->id})\n\n";

// 2. 测试获取预警规则列表（无分页参数）
echo "2. 测试获取预警规则列表（无分页参数）...\n";
$request1 = new Request();
$controller = new AlertRuleController();

try {
    $response1 = $controller->index($request1);
    $data1 = $response1->getData(true);
    
    if ($data1['success']) {
        $rules1 = $data1['data']['data'] ?? [];
        echo "   返回规则数: " . count($rules1) . "\n";
        echo "   总数: " . ($data1['data']['total'] ?? 'N/A') . "\n";
        echo "   当前页: " . ($data1['data']['current_page'] ?? 'N/A') . "\n";
        echo "   每页数量: " . ($data1['data']['per_page'] ?? 'N/A') . "\n";
    } else {
        echo "   失败: " . ($data1['message'] ?? '未知错误') . "\n";
    }
} catch (\Exception $e) {
    echo "   异常: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. 测试获取预警规则列表（大分页参数）
echo "3. 测试获取预警规则列表（大分页参数）...\n";
$request2 = new Request();
$request2->merge(['per_page' => 1000]);

try {
    $response2 = $controller->index($request2);
    $data2 = $response2->getData(true);
    
    if ($data2['success']) {
        $rules2 = $data2['data']['data'] ?? [];
        echo "   返回规则数: " . count($rules2) . "\n";
        echo "   总数: " . ($data2['data']['total'] ?? 'N/A') . "\n";
        
        // 显示前几个规则的详细信息
        echo "   前3个规则详情:\n";
        foreach (array_slice($rules2, 0, 3) as $rule) {
            echo "     - ID: {$rule['id']}, 名称: {$rule['name']}\n";
            echo "       类型: " . json_encode($rule['rule_type']) . "\n";
        }
    } else {
        echo "   失败: " . ($data2['message'] ?? '未知错误') . "\n";
    }
} catch (\Exception $e) {
    echo "   异常: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. 直接查询数据库对比
echo "4. 直接查询数据库对比...\n";
$dbRules = AlertRule::where('user_id', $user->id)->get();
echo "   数据库中该用户的规则数: " . $dbRules->count() . "\n";

foreach ($dbRules->take(3) as $rule) {
    echo "     - ID: {$rule->id}, 名称: {$rule->name}\n";
    echo "       类型: " . json_encode($rule->rule_type) . "\n";
}

echo "\n=== 测试完成 ===\n";
