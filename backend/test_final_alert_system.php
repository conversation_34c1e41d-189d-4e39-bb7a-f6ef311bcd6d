<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\TaskGroup;
use App\Models\MonitoringTask;
use App\Models\ProductData;
use App\Models\TaskGroupAlertSummary;
use App\Services\AlertService;
use App\Services\TaskGroupSummaryService;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\DB;

echo "=== 预警系统完整测试 ===\n\n";

// 1. 清理测试数据
echo "1. 清理现有测试数据...\n";
$user = User::find(1);
if ($user) {
    $user->notifications()->delete();
}
TaskGroupAlertSummary::where('task_group_id', 1)->delete();
echo "✓ 清理完成\n\n";

// 2. 检查基础数据
echo "2. 检查基础数据...\n";
echo "用户: " . ($user ? $user->username : "未找到") . "\n";

$taskGroup = TaskGroup::find(1);
if (!$taskGroup) {
    echo "❌ 任务组ID 1不存在，查找所有可用的任务组...\n";
    $taskGroups = TaskGroup::with('monitoringTasks')->get();
    if ($taskGroups->isEmpty()) {
        echo "❌ 没有找到任何任务组\n";
        exit(1);
    }
    
    echo "可用的任务组:\n";
    foreach($taskGroups as $tg) {
        echo "  - ID: {$tg->id}, 名称: {$tg->name}, 监控任务数: " . $tg->monitoringTasks->count() . "\n";
    }
    
    // 选择第一个有监控任务的任务组
    $taskGroup = $taskGroups->filter(function($tg) {
        return $tg->monitoringTasks->count() > 0;
    })->first();
    
    if (!$taskGroup) {
        echo "❌ 没有找到包含监控任务的任务组\n";
        exit(1);
    }
}

echo "任务组: " . $taskGroup->name . " (ID: {$taskGroup->id})\n";

// 获取任务组下的监控任务和商品数据
$monitoringTasks = $taskGroup->monitoringTasks;
echo "监控任务数量: " . $monitoringTasks->count() . "\n";

$totalProducts = 0;
$productDetails = [];

foreach($monitoringTasks as $task) {
    $products = ProductData::where('monitoring_task_id', $task->id)->get();
    $totalProducts += $products->count();
    echo "  任务 {$task->name} (ID: {$task->id}): {$products->count()} 个商品\n";
    
    foreach($products as $product) {
        // 检查商品的SKU数据和偏离率
        $skus = $product->skus;
        foreach($skus as $sku) {
            $promotionDeviation = $sku->promotion_deviation_rate;
            $channelDeviation = $sku->channel_price_deviation_rate;
            
            if ($promotionDeviation > 1 || $channelDeviation > 1) {
                $productDetails[] = [
                    'product_id' => $product->item_id,
                    'title' => $product->title,
                    'sku_name' => $sku->name,
                    'promotion_deviation' => $promotionDeviation,
                    'channel_deviation' => $channelDeviation,
                    'price' => $sku->price,
                    'sub_price' => $sku->sub_price,
                    'official_guide_price' => $sku->official_guide_price,
                ];
            }
        }
    }
}

echo "总商品数量: $totalProducts\n";
echo "符合预警条件的商品:\n";
foreach($productDetails as $detail) {
    echo "  商品 {$detail['product_id']}: 促销偏离={$detail['promotion_deviation']}%, 渠道偏离={$detail['channel_deviation']}%\n";
}
echo "\n";

// 3. 模拟任务组采集完成
echo "3. 模拟任务组采集完成...\n";
$alertService = app(AlertService::class);

// 生成一个采集批次ID
$collectionBatchId = "test_batch_" . now()->format('YmdHis') . '_' . uniqid();
echo "使用采集批次ID: $collectionBatchId\n";

try {
    // 为每个监控任务调用完成方法
    foreach($monitoringTasks as $task) {
        echo "  处理监控任务: {$task->name} (ID: {$task->id})\n";
        $alertService->completeTaskGroupCollection($task->id, $collectionBatchId);
    }
    echo "✓ 任务组采集完成处理成功\n";
} catch (Exception $e) {
    echo "❌ 任务组采集完成处理失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
echo "\n";

// 4. 检查队列状态
echo "4. 检查队列状态...\n";
$queueSize = DB::table('jobs')->count();
echo "队列任务数量: $queueSize\n";

if ($queueSize > 0) {
    echo "队列中的任务:\n";
    $jobs = DB::table('jobs')->orderBy('created_at', 'desc')->take(5)->get();
    foreach($jobs as $job) {
        $payload = json_decode($job->payload, true);
        $jobName = $payload['displayName'] ?? 'Unknown';
        echo "  - $jobName (队列: {$job->queue})\n";
    }
}
echo "\n";

// 5. 等待队列处理
echo "5. 等待队列处理...\n";
$maxWait = 30; // 最多等待30秒
$waited = 0;
while ($waited < $maxWait) {
    $queueSize = DB::table('jobs')->count();
    if ($queueSize == 0) {
        echo "✓ 所有队列任务已处理完成\n";
        break;
    }
    echo "  等待中... 剩余任务: $queueSize\n";
    sleep(2);
    $waited += 2;
}

if ($waited >= $maxWait) {
    echo "⚠ 超时，仍有 $queueSize 个任务未处理\n";
}
echo "\n";

// 6. 检查预警汇总
echo "6. 检查预警汇总...\n";
$summaries = TaskGroupAlertSummary::where('task_group_id', $taskGroup->id)->get();
echo "汇总记录数量: " . $summaries->count() . "\n";

foreach($summaries as $summary) {
    echo "  汇总ID: {$summary->id}\n";
    echo "  任务组ID: {$summary->task_group_id}\n";
    echo "  预警数量: {$summary->alert_count}\n";
    echo "  商品数量: {$summary->product_count}\n";
    echo "  状态: {$summary->status}\n";
    echo "  创建时间: {$summary->created_at}\n";
    echo "  汇总数据: " . json_encode($summary->summary_data, JSON_UNESCAPED_UNICODE) . "\n";
    echo "\n";
}

// 7. 检查通知
echo "7. 检查通知...\n";
if ($user) {
    $notifications = $user->notifications()
        ->orderBy('created_at', 'desc')
        ->take(5)
        ->get();

    echo "通知记录数量: " . $notifications->count() . "\n";

    foreach($notifications as $notification) {
        echo "  通知ID: {$notification->id}\n";
        echo "  类型: {$notification->type}\n";
        $data = $notification->data;
        echo "  标题: " . ($data['title'] ?? 'N/A') . "\n";
        echo "  内容: " . ($data['message'] ?? 'N/A') . "\n";
        echo "  是否已读: " . ($notification->read_at ? '是' : '否') . "\n";
        echo "  创建时间: {$notification->created_at}\n";
        echo "\n";
    }
}

// 8. 测试API响应
echo "8. 测试API响应...\n";
try {
    if ($user) {
        $notificationsResponse = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        
        echo "API通知数量: " . $notificationsResponse->total() . "\n";
        echo "当前页通知: " . $notificationsResponse->count() . "\n";
    }
} catch (Exception $e) {
    echo "❌ API测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n"; 