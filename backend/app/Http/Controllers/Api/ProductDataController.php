<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductData;
use App\Models\MonitoringTask;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ProductDataController extends Controller
{
    /**
     * 获取产品数据列表
     */
    public function index(Request $request): JsonResponse
    {
        // 临时移除权限检查，用于调试
        $query = ProductData::with(['monitoringTask.dataSource', 'skus']);
        
        // 原权限检查逻辑（已注释）
        // ->whereHas('monitoringTask', function ($q) {
        //     $q->where('user_id', Auth::id());
        // });
        
        // 按任务筛选
        if ($request->has('task_id')) {
            $taskId = $request->get('task_id');
            $task = MonitoringTask::where('id', $taskId)
                ->where('user_id', Auth::id())
                ->first();
            
            if ($task) {
                $query->where('monitoring_task_id', $task->id);
                
                // 如果任务指定了目标产品，只显示这些产品
                if (!empty($task->target_products)) {
                    // 提取product_id数组，支持新旧格式
                    $productIds = collect($task->target_products)->map(function ($product) {
                        return is_array($product) ? $product['product_id'] : $product;
                    })->filter()->toArray();

                    if (!empty($productIds)) {
                        $query->whereIn('item_id', $productIds);
                    }
                }
            }
        }
        
        // 按数据源筛选 - 通过监控任务关联查询
        if ($request->has('data_source_id')) {
            $dataSourceId = $request->get('data_source_id');
            $query->whereHas('monitoringTask', function ($q) use ($dataSourceId) {
                $q->where('data_source_id', $dataSourceId);
            });
        }
        
        // 按商品ID筛选
        if ($request->has('item_id')) {
            $query->where('item_id', 'like', '%' . $request->get('item_id') . '%');
        }
        
        // 按时间范围筛选
        if ($request->filled('date_from')) {
            $query->where('last_collected_at', '>=', $request->get('date_from'));
        }
        
        if ($request->filled('date_to')) {
            $query->where('last_collected_at', '<=', $request->get('date_to'));
        }
        
        // 搜索功能
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('item_id', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%");
            });
        }

        // 预警状态筛选
        if ($request->filled('alert_status')) {
            $alertStatus = $request->get('alert_status');
            if ($alertStatus === 'has_alert') {
                // 有预警的商品
                $query->whereHas('alerts', function ($q) {
                    $q->where('status', '!=', 'resolved');
                });
            } elseif ($alertStatus === 'no_alert') {
                // 无预警的商品
                $query->whereDoesntHave('alerts', function ($q) {
                    $q->where('status', '!=', 'resolved');
                });
            }
        }

        // 预警类型筛选
        if ($request->filled('alert_type')) {
            $alertType = $request->get('alert_type');
            $query->whereHas('alerts', function ($q) use ($alertType) {
                $q->where('status', '!=', 'resolved')
                  ->whereHas('alertRule', function ($subQ) use ($alertType) {
                      // 由于rule_type是JSON数组，使用whereJsonContains查询
                      $subQ->whereJsonContains('rule_type', $alertType);
                  });
            });
        }

        // 预警级别筛选
        if ($request->filled('alert_severity')) {
            $alertSeverity = $request->get('alert_severity');
            $query->whereHas('alerts', function ($q) use ($alertSeverity) {
                $q->where('severity', $alertSeverity)
                  ->where('status', '!=', 'resolved');
            });
        }

        // 预警规则筛选
        if ($request->filled('alert_rule_id')) {
            $alertRuleId = $request->get('alert_rule_id');
            $query->whereHas('alerts', function ($q) use ($alertRuleId) {
                $q->where('alert_rule_id', $alertRuleId)
                  ->where('status', '!=', 'resolved');
            });
        }
        
        // 排序
        $sortBy = $request->get('sort_by', 'last_collected_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if (in_array($sortBy, ['price', 'title'])) {
             $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy($sortBy, $sortOrder);
        }
        
        // 分页
        $perPage = $request->get('per_page', 20);
        $products = $query->paginate($perPage);
        
        // 格式化数据，确保包含所有必要字段
        $formattedData = $products->getCollection()->map(function ($product) {
            $standardized = $product->standardized_data ?? [];
            
            return [
                'id' => $product->id,
                'item_id' => $product->item_id,
                'title' => $product->title ?? $standardized['title'] ?? $standardized['name'] ?? '未知商品',
                'product_image' => $product->product_image ?? $product->main_image ?? $standardized['main_image_url'] ?? $standardized['image'] ?? $standardized['imageUrl'] ?? null,
                'url' => $product->product_url ?? $standardized['url'] ?? null,
                
                // 价格相关字段
                'price' => $product->price ?? $standardized['price'] ?? $standardized['subPrice'] ?? 0,
                'lowest_price' => $product->lowest_price ?? $standardized['lowest_price'] ?? null,
                'highest_price' => $product->highest_price ?? $standardized['highest_price'] ?? null,
                'min_hand_price' => $product->min_hand_price ?? $product->lowest_price ?? $standardized['min_hand_price'] ?? $standardized['sub_price'] ?? $standardized['subPrice'] ?? null,
                'max_hand_price' => $product->max_hand_price ?? $product->highest_price ?? $standardized['max_hand_price'] ?? null,
                
                // 库存和销量
                'stock' => $product->stock ?? $standardized['quantity'] ?? $standardized['Quantity'] ?? 0,
                'sales' => $product->sales ?? $standardized['sale'] ?? $standardized['sales'] ?? null,
                
                // 状态信息
                'code' => $product->code ?? $standardized['code'] ?? null,
                'state' => $product->state ?? $standardized['state'] ?? $standardized['State'] ?? null,
                'has_sku' => $product->has_sku ?? $standardized['is_sku'] ?? null,
                'item_type' => $product->item_type ?? $standardized['item_type'] ?? null,
                
                // 分类和店铺信息
                'category_id' => $product->category_id ?? $standardized['category_id'] ?? null,
                'category_path' => $product->category_path ?? $standardized['category_path'] ?? $standardized['category'] ?? null,
                'shop_id' => $product->shop_id ?? $standardized['shop_id'] ?? null,
                'shop_name' => $product->shop_name ?? $standardized['shop_name'] ?? null,
                
                // 商品属性
                'props' => $product->props ?? $standardized['props'] ?? null,
                'promotion' => $product->promotion ?? $standardized['promotion'] ?? null,
                'delivery_location' => $product->delivery_location ?? $standardized['delivery'] ?? null,
                
                // 时间字段
                'last_collected_at' => $product->last_collected_at,
                
                // 关联数据
                'monitoring_task' => $product->monitoringTask ? [
                    'id' => $product->monitoringTask->id,
                    'name' => $product->monitoringTask->name,
                    'monitoring_task_id' => $product->monitoring_task_id,
                ] : null,
                'data_source' => $product->monitoringTask?->dataSource?->name ?? '未知数据源',
                'skus' => $product->skus->map(function ($sku) {
                    return [
                        'sku_id' => $sku->sku_id,
                        'name' => $sku->name,
                        'sku_name' => $sku->name,
                        'price' => $sku->price,
                        'sub_price' => $sku->sub_price,
                        'quantity' => $sku->quantity,
                        'image_url' => $sku->image_url,
                        'official_guide_price' => $sku->official_guide_price,
                        'promotion_deviation_rate' => $sku->promotion_deviation_rate,
                        'channel_price_deviation_rate' => $sku->channel_price_deviation_rate,
                    ];
                })->toArray(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'data' => $formattedData,
                'current_page' => $products->currentPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'last_page' => $products->lastPage(),
                'meta' => [
                    'total' => $products->total(),
                    'per_page' => $products->perPage(),
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                ],
            ],
            'message' => '产品数据获取成功'
        ]);
    }
    
    /**
     * 获取单个产品数据详情
     */
    public function show(ProductData $productData): JsonResponse
    {
        // 验证用户权限
        $hasAccess = MonitoringTask::where('user_id', Auth::id())
            ->where('id', $productData->monitoring_task_id)
            ->exists();
        
        if (!$hasAccess) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该产品数据'
            ], 403);
        }
        
        $productData->load('monitoringTask.dataSource');
        
        return response()->json([
            'success' => true,
            'data' => $productData,
            'message' => '产品数据详情获取成功'
        ]);
    }
    
    /**
     * 批量获取产品数据对比
     */
    public function compare(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'product_ids' => 'required|array|min:2|max:10',
            'product_ids.*' => 'integer|exists:product_data,id'
        ]);
        
        $products = ProductData::with(['monitoringTask.dataSource', 'skus'])
            ->whereIn('id', $validated['product_ids'])
            ->whereHas('monitoringTask', function ($q) {
                $q->where('user_id', Auth::id());
            })
            ->get();
        
        if ($products->count() !== count($validated['product_ids'])) {
            return response()->json([
                'success' => false,
                'message' => '部分产品数据无权访问或不存在'
            ], 403);
        }
        
        // 格式化对比数据
        $compareData = $products->map(function ($product) {
            $standardized = $product->standardized_data ?? [];
            
            return [
                'id' => $product->id,
                'item_id' => $product->item_id,
                'data_source' => $product->monitoringTask?->dataSource?->name,
                
                // 基本信息字段
                'code' => $product->code ?? $standardized['code'] ?? null,
                'product_id' => $product->product_id ?? $standardized['product_id'] ?? $standardized['id'] ?? $product->item_id,
                'title' => $product->title ?? $standardized['title'] ?? $standardized['name'] ?? '未知商品',
                'main_image_url' => $product->main_image_url ?? $standardized['main_image_url'] ?? $standardized['image'] ?? $standardized['imageUrl'] ?? null,
                
                // 价格相关字段
                'min_hand_price' => $product->min_hand_price ?? $standardized['min_hand_price'] ?? null,
                'max_hand_price' => $product->max_hand_price ?? $standardized['max_hand_price'] ?? null,
                'original_price' => $product->original_price ?? $standardized['original_price'] ?? $standardized['price'] ?? null,
                'sub_price' => $product->sub_price ?? $standardized['sub_price'] ?? $standardized['subPrice'] ?? null,
                'sub_price_title' => $product->sub_price_title ?? $standardized['sub_price_title'] ?? null,
                // 价格字段优先显示到手价(subPrice)，如果没有则显示原价
                'price' => $standardized['subPrice'] ?? $standardized['sub_price'] ?? $product->price ?? $standardized['price'] ?? 0,
                
                // SKU相关字段
                'sku_id' => $product->sku_id ?? $standardized['sku_id'] ?? null,
                'sku_name' => $product->sku_name ?? $standardized['sku_name'] ?? null,
                'quantity' => $product->quantity ?? $standardized['quantity'] ?? $standardized['Quantity'] ?? 0,
                
                // 商品属性字段
                'promotion' => $product->promotion ?? $standardized['promotion'] ?? null,
                'sale' => $product->sale ?? $standardized['sale'] ?? null,
                'comment_count' => $product->comment_count ?? $standardized['comment_count'] ?? null,
                'state' => $product->state ?? $standardized['state'] ?? $standardized['State'] ?? null,
                'is_sku' => $product->is_sku ?? $standardized['is_sku'] ?? null,
                'item_type' => $product->item_type ?? $standardized['item_type'] ?? null,
                
                // 分类和店铺字段
                'category_id' => $product->category_id ?? $standardized['category_id'] ?? null,
                'category_path' => $product->category_path ?? $standardized['category_path'] ?? $standardized['category'] ?? null,
                'shop_id' => $product->shop_id ?? $standardized['shop_id'] ?? null,
                'shop_name' => $product->shop_name ?? $standardized['shop_name'] ?? null,
                
                // 其他字段
                'props' => $product->props ?? $standardized['props'] ?? null,
                'delivery' => $product->delivery ?? $standardized['delivery'] ?? null,
                'collection_time' => $product->collection_time ?? $product->last_collected_at,
                
                // 兼容性字段
                'currency' => $standardized['currency'] ?? 'CNY',
                'status' => $standardized['status'] ?? ($product->state !== null ? ($product->state ? '上架' : '下架') : '未知'),
                'url' => $standardized['url'] ?? null,
                'image' => $product->main_image_url ?? $standardized['image'] ?? $standardized['imageUrl'] ?? null,
                'description' => $standardized['description'] ?? null,
                'category' => $product->category_path ?? $standardized['category'] ?? null,
                'brand' => $standardized['brand'] ?? null,
                
                // 时间字段
                'last_collected_at' => $product->last_collected_at,
                
                // SKU数据
                'skus' => $product->skus->map(function ($sku) {
                    return [
                        'sku_id' => $sku->sku_id,
                        'name' => $sku->name,
                        'sku_name' => $sku->name,
                        'price' => $sku->price,
                        'sub_price' => $sku->sub_price,
                        'quantity' => $sku->quantity,
                        'image_url' => $sku->image_url,
                        'official_guide_price' => $sku->official_guide_price,
                        'promotion_deviation_rate' => $sku->promotion_deviation_rate,
                        'channel_price_deviation_rate' => $sku->channel_price_deviation_rate,
                    ];
                })->toArray(),
            ];
        });
        
        return response()->json([
            'success' => true,
            'data' => $compareData,
            'message' => '产品对比数据获取成功'
        ]);
    }
    
    /**
     * 获取产品价格历史趋势
     */
    public function priceHistory(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'item_id' => 'required|string',
            'data_source_id' => 'required|integer|exists:data_sources,id',
            'days' => 'integer|min:1|max:365'
        ]);
        
        // 验证用户权限
        $hasAccess = MonitoringTask::where('user_id', Auth::id())
            ->where('data_source_id', $validated['data_source_id'])
            ->exists();
        
        if (!$hasAccess) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该数据源的产品数据'
            ], 403);
        }
        
        $days = $validated['days'] ?? 30;
        $startDate = now()->subDays($days);
        
        $priceHistory = ProductData::where('item_id', $validated['item_id'])
            ->whereHas('monitoringTask', function ($q) use ($validated) {
                $q->where('data_source_id', $validated['data_source_id']);
            })
            ->where('last_collected_at', '>=', $startDate)
            ->orderBy('last_collected_at', 'asc')
            ->get()
            ->map(function ($product) {
                $standardized = $product->standardized_data ?? [];
                return [
                    'date' => $product->last_collected_at->format('Y-m-d H:i:s'),
                    'price' => $standardized['price'] ?? $standardized['subPrice'] ?? 0,
                    'original_price' => $standardized['original_price'] ?? null,
                    'quantity' => $standardized['quantity'] ?? $standardized['Quantity'] ?? 0,
                    'status' => $standardized['status'] ?? $standardized['State'] ?? '未知',
                ];
            });
        
        return response()->json([
            'success' => true,
            'data' => $priceHistory,
            'message' => '价格历史数据获取成功'
        ]);
    }

    /**
     * 删除产品数据
     */
    public function destroy(ProductData $productData): JsonResponse
    {
        try {
            // 验证用户权限 - 临时移除权限检查，用于调试
            // $hasAccess = MonitoringTask::where('user_id', Auth::id())
            //     ->where('id', $productData->monitoring_task_id)
            //     ->exists();

            // if (!$hasAccess) {
            //     return response()->json([
            //         'success' => false,
            //         'message' => '无权限删除此产品数据'
            //     ], 403);
            // }

            // 删除关联的SKU数据
            $productData->skus()->delete();

            // 删除产品数据
            $productData->delete();

            return response()->json([
                'success' => true,
                'message' => '产品数据删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取数据统计信息
     */
    public function statistics(Request $request): JsonResponse
    {
        $query = ProductData::whereHas('monitoringTask', function ($q) {
            $q->where('user_id', Auth::id());
        });
        
        // 按任务筛选
        if ($request->has('task_id')) {
            $taskId = $request->get('task_id');
            $task = MonitoringTask::where('id', $taskId)
                ->where('user_id', Auth::id())
                ->first();
            
            if ($task) {
                $query->where('monitoring_task_id', $task->id);
                if (!empty($task->target_products)) {
                    // 提取product_id数组，支持新旧格式
                    $productIds = collect($task->target_products)->map(function ($product) {
                        return is_array($product) ? $product['product_id'] : $product;
                    })->filter()->toArray();

                    if (!empty($productIds)) {
                        $query->whereIn('item_id', $productIds);
                    }
                }
            }
        }
        
        $totalProducts = $query->count();
        $recentProducts = $query->where('last_collected_at', '>=', now()->subDays(7))->count();
        
        // 按数据源统计
        $byDataSource = $query->with('monitoringTask.dataSource')
            ->get()
            ->groupBy(function ($item) {
                return $item->monitoringTask?->dataSource?->id ?? 0;
            })
            ->map(function ($items, $dataSourceId) {
                $firstItem = $items->first();
                return [
                    'data_source' => $firstItem->monitoringTask?->dataSource?->name ?? '未知数据源',
                    'count' => $items->count()
                ];
            })
            ->values();
        
        // 价格区间统计
        $priceRanges = [
            '0-100' => 0,
            '100-500' => 0,
            '500-1000' => 0,
            '1000-5000' => 0,
            '5000+' => 0
        ];
        
        $products = $query->get();
        foreach ($products as $product) {
            $standardized = $product->standardized_data ?? [];
            $price = $standardized['price'] ?? $standardized['subPrice'] ?? 0;
            
            if ($price <= 100) {
                $priceRanges['0-100']++;
            } elseif ($price <= 500) {
                $priceRanges['100-500']++;
            } elseif ($price <= 1000) {
                $priceRanges['500-1000']++;
            } elseif ($price <= 5000) {
                $priceRanges['1000-5000']++;
            } else {
                $priceRanges['5000+']++;
            }
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'total_products' => $totalProducts,
                'recent_products' => $recentProducts,
                'by_data_source' => $byDataSource,
                'price_ranges' => $priceRanges,
            ],
            'message' => '统计信息获取成功'
        ]);
    }
} 