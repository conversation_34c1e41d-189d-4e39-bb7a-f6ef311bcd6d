<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Lara<PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'name',
        'email',
        'password',
        'real_name',
        'phone',
        'status',
        'last_login_at',
        'last_login_ip',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'password' => 'hashed',
            'status' => 'integer',
        ];
    }

    /**
     * 用户拥有的角色
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_roles', 'user_id', 'role_id')
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * 检查用户是否拥有指定角色
     */
    public function hasRole(string $roleName): bool
    {
        return $this->roles()->where('name', $roleName)->exists();
    }

    /**
     * 检查用户是否拥有指定权限
     */
    public function hasPermission(string $permissionName): bool
    {
        return $this->roles()
                    ->whereHas('permissions', function ($query) use ($permissionName) {
                        $query->where('name', $permissionName);
                    })
                    ->exists();
    }

    /**
     * 获取用户的所有权限
     */
    public function getAllPermissions()
    {
        return Permission::whereHas('roles.users', function ($query) {
            $query->where('user_id', $this->id);
        })->get();
    }

    /**
     * 用户创建的监控任务
     */
    public function monitoringTasks()
    {
        return $this->hasMany(MonitoringTask::class);
    }

    /**
     * 用户创建的任务组
     */
    public function taskGroups()
    {
        return $this->hasMany(TaskGroup::class);
    }

    /**
     * 用户的预警规则
     */
    public function alertRules()
    {
        return $this->hasMany(AlertRule::class);
    }

    /**
     * 用户相关的预警
     */
    public function alerts()
    {
        return $this->hasMany(Alert::class);
    }
}
