<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\AsArrayObject;

class ProductData extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'product_data';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'monitoring_task_id',
        'item_id',
        'collection_batch_id',
        'standardized_data',
        'last_collected_at',
        'code',
        'title',
        'product_image',
        'price',
        'lowest_price',
        'highest_price',
        'stock',
        'sales',
        'comment_count',
        'state',
        'has_sku',
        'item_type',
        'category_id',
        'category_path',
        'shop_id',
        'shop_name',
        'props',
        'promotion_info',
        'delivery_location',
        'product_url',
        'product_status',
        'api_status',
        'promotion',
        'min_hand_price',
        'max_hand_price',
        'official_guide_price',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'standardized_data' => 'array',
        'props' => 'json',
        'promotion_info' => 'json',
        'last_collected_at' => 'datetime',
        'price' => 'decimal:2',
        'lowest_price' => 'decimal:2',
        'highest_price' => 'decimal:2',
        'stock' => 'integer',
        'sales' => 'integer',
        'comment_count' => 'integer',
        'code' => 'integer',
        'shop_id' => 'integer',
        'category_id' => 'integer',
        'product_status' => 'integer',
        'api_status' => 'integer',
        'captured_at' => 'datetime',
        'promotion' => 'json',
        'min_hand_price' => 'decimal:2',
        'max_hand_price' => 'decimal:2',
        'official_guide_price' => 'decimal:2',
        'has_sku' => 'boolean',
    ];

    /**
     * The attributes that should be appended to the model's array form.
     *
     * @var array
     */
    protected $appends = [];

    /**
     * Get the main product image.
     *
     * @return string|null
     */
    public function getMainImageAttribute(): ?string
    {
        // 首先检查product_image字段
        if (!empty($this->product_image)) {
            return $this->product_image;
        }

        // 最后检查standardized_data中的图片
        $data = $this->standardized_data;
        
        if (isset($data['main_imgs']) && is_array($data['main_imgs']) && !empty($data['main_imgs'][0])) {
            return $data['main_imgs'][0];
        }
        
        if (isset($data['pic_urls']) && is_array($data['pic_urls']) && !empty($data['pic_urls'][0])) {
            return $data['pic_urls'][0];
        }

        return null;
    }

    /**
     * Get the total stock of all SKUs for the product.
     *
     * @return int
     */
    public function getTotalStockAttribute(): int
    {
        // 通过关联关系加载SKUs并计算quantity总和
        // a more efficient way is to use withSum, but this works fine for accessors
        return $this->skus->sum('quantity');
    }

    /**
     * 获取数据源
     */
    public function dataSource(): BelongsTo
    {
        return $this->belongsTo(DataSource::class);
    }

    /**
     * 获取监控任务
     */
    public function monitoringTask(): BelongsTo
    {
        return $this->belongsTo(MonitoringTask::class);
    }

    /**
     * 通过监控任务获取数据源
     */
    public function getDataSourceAttribute()
    {
        return $this->monitoringTask?->dataSource;
    }

    public function skus(): HasMany
    {
        return $this->hasMany(ProductSku::class);
    }

    /**
     * 获取相关的预警
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(Alert::class, 'product_id', 'item_id');
    }
}