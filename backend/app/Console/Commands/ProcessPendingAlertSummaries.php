<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TaskGroupAlertSummary;
use App\Services\AlertService;
use App\Services\TaskGroupAlertSummaryService;
use Illuminate\Support\Facades\Log;

class ProcessPendingAlertSummaries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'alerts:process-pending-summaries {--dry-run : 只显示待处理的汇总，不实际处理}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '处理所有待处理的预警汇总，确保汇总通知能够发送';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始检查待处理的预警汇总...');

        // 查找所有状态为 collecting 的汇总记录
        $pendingSummaries = TaskGroupAlertSummary::where('status', TaskGroupAlertSummary::STATUS_COLLECTING)
            ->where('created_at', '<=', now()->subMinutes(5)) // 创建时间超过5分钟的
            ->orderBy('created_at', 'asc')
            ->get();

        if ($pendingSummaries->isEmpty()) {
            $this->info('没有找到待处理的预警汇总。');
            return 0;
        }

        $this->info("找到 {$pendingSummaries->count()} 个待处理的预警汇总：");

        $summaryService = new TaskGroupAlertSummaryService();
        $alertService = new AlertService($summaryService);

        foreach ($pendingSummaries as $summary) {
            $this->line("- ID: {$summary->id}, 批次: {$summary->collection_batch_id}, 预警数: {$summary->alert_count}, 创建时间: {$summary->created_at}");

            if (!$this->option('dry-run')) {
                try {
                    $this->info("  处理汇总 ID: {$summary->id}...");
                    
                    // 手动完成汇总
                    $alertService->completeTaskGroupCollection(
                        $summary->monitoring_task_id,
                        $summary->collection_batch_id
                    );

                    // 刷新状态
                    $summary->refresh();
                    $this->info("  ✓ 汇总处理完成，新状态: {$summary->status}");

                } catch (\Exception $e) {
                    $this->error("  ✗ 处理汇总失败: " . $e->getMessage());
                    Log::error('手动处理预警汇总失败', [
                        'summary_id' => $summary->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        if ($this->option('dry-run')) {
            $this->info('这是预览模式，没有实际处理任何汇总。使用 --no-dry-run 来实际处理。');
        } else {
            $this->info('所有待处理的预警汇总已处理完成。');
        }

        return 0;
    }
}
