<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\BulkProcessingService;
use App\Models\MonitoringTask;
use App\Models\ProductData;
use App\Models\User;

class SystemCapacityReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:capacity-report 
                            {--products=30000 : 测试商品数量}
                            {--show-recommendations : 显示优化建议}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '生成系统处理能力评估报告';

    /**
     * 批量处理服务
     */
    private BulkProcessingService $bulkService;

    /**
     * 构造函数
     */
    public function __construct(BulkProcessingService $bulkService)
    {
        parent::__construct();
        $this->bulkService = $bulkService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 系统处理能力评估报告');
        $this->newLine();

        // 1. 系统当前状态
        $this->displaySystemStatus();

        // 2. 用户和任务统计
        $this->displayUserStatistics();

        // 3. 系统容量评估
        $this->displayCapacityAssessment();

        // 4. 大规模处理测试
        $testProducts = $this->option('products');
        $this->displayBulkProcessingAssessment((int) $testProducts);

        // 5. 优化建议
        if ($this->option('show-recommendations')) {
            $this->displayRecommendations();
        }

        $this->newLine();
        $this->info('✅ 报告生成完成');
    }

    /**
     * 显示系统当前状态
     */
    private function displaySystemStatus(): void
    {
        $this->info('📊 系统当前状态');
        
        $capacity = $this->bulkService->getSystemCapacity();
        
        $statusTable = [
            ['指标', '当前值', '状态'],
            ['队列积压任务', $capacity['queue_statistics']['total_pending'] ?? 0, $this->getQueueStatus($capacity['queue_statistics']['total_pending'] ?? 0)],
            ['内存使用率', ($capacity['system_load']['memory_usage_percentage'] ?? 0) . '%', $this->getMemoryStatus($capacity['system_load']['memory_usage_percentage'] ?? 0)],
            ['活跃Worker数', $capacity['system_load']['active_workers'] ?? 0, $this->getWorkerStatus($capacity['system_load']['active_workers'] ?? 0)],
            ['推荐批次大小', $capacity['recommended_batch_size'] ?? 100, '正常'],
            ['最优Worker数', $capacity['optimal_worker_count'] ?? 4, '正常'],
        ];

        $this->table(['指标', '当前值', '状态'], array_slice($statusTable, 1));
        $this->newLine();
    }

    /**
     * 显示用户和任务统计
     */
    private function displayUserStatistics(): void
    {
        $this->info('👥 用户和任务统计');

        $totalUsers = User::count();
        $activeUsers = User::whereHas('monitoringTasks', function ($query) {
            $query->where('status', 'running');
        })->count();

        $totalTasks = MonitoringTask::count();
        $runningTasks = MonitoringTask::where('status', 'running')->count();
        
        $todayProducts = ProductData::whereDate('created_at', today())->count();
        $weekProducts = ProductData::where('created_at', '>=', now()->subDays(7))->count();

        $userTable = [
            ['统计项', '数量', '说明'],
            ['总用户数', $totalUsers, '系统注册用户'],
            ['活跃用户数', $activeUsers, '有运行中任务的用户'],
            ['总监控任务', $totalTasks, '所有监控任务'],
            ['运行中任务', $runningTasks, '当前正在运行的任务'],
            ['今日采集商品', $todayProducts, '今日新增商品数据'],
            ['本周采集商品', $weekProducts, '本周累计商品数据'],
        ];

        $this->table(['统计项', '数量', '说明'], array_slice($userTable, 1));
        $this->newLine();
    }

    /**
     * 显示系统容量评估
     */
    private function displayCapacityAssessment(): void
    {
        $this->info('🚀 系统处理能力评估');

        $capacity = $this->bulkService->getSystemCapacity();
        $dailyCapacity = $capacity['estimated_daily_capacity'] ?? 0;

        $capacityTable = [
            ['时间周期', '处理能力', '30万商品支持情况'],
            ['每小时', number_format($dailyCapacity / 24), $this->getCapacityStatus($dailyCapacity / 24, 12500)],
            ['每天', number_format($dailyCapacity), $this->getCapacityStatus($dailyCapacity, 300000)],
            ['每周', number_format($dailyCapacity * 7), $this->getCapacityStatus($dailyCapacity * 7, 2100000)],
            ['每月', number_format($dailyCapacity * 30), $this->getCapacityStatus($dailyCapacity * 30, 9000000)],
        ];

        $this->table(['时间周期', '处理能力 (商品数)', '30万商品支持情况'], array_slice($capacityTable, 1));

        // 特别说明30万商品峰值处理
        $this->newLine();
        $this->warn('⚠️  30万商品峰值处理分析:');
        
        if ($dailyCapacity >= 300000) {
            $this->info('✅ 系统当前能力足以支持30万商品的日处理量');
        } else {
            $shortfall = 300000 - $dailyCapacity;
            $this->error("❌ 系统当前能力不足，缺口: " . number_format($shortfall) . " 商品/天");
            $this->warn("💡 建议增加 " . ceil($shortfall / ($dailyCapacity / ($capacity['optimal_worker_count'] ?? 4))) . " 个Worker进程");
        }

        $this->newLine();
    }

    /**
     * 显示大规模处理评估
     */
    private function displayBulkProcessingAssessment(int $testProducts): void
    {
        $this->info("🧪 大规模处理评估 ({$testProducts}个商品)");

        // 模拟一个监控任务来测试
        $mockTask = new MonitoringTask([
            'id' => 999,
            'data_source_id' => 1,
            'name' => '测试任务',
        ]);

        try {
            $strategy = $this->bulkService->getOptimalBatchStrategy($testProducts, $mockTask);

            $strategyTable = [
                ['评估项', '推荐值', '说明'],
                ['推荐批次大小', $strategy['recommended_batch_size'], '每批处理的商品数量'],
                ['预计批次数', $strategy['estimated_batches'], '总共需要的批次数'],
                ['预计处理时间', $strategy['estimated_processing_time_minutes'] . ' 分钟', '完成全部处理的时间'],
                ['推荐Worker数', $strategy['recommended_worker_count'], '并发处理的进程数'],
                ['预计内存使用', $strategy['peak_memory_estimate_mb'] . ' MB', '峰值内存占用'],
                ['是否需要扩容', $strategy['should_use_queue_scaling'] ? '是' : '否', '是否需要启用队列扩容'],
            ];

            $this->table(['评估项', '推荐值', '说明'], array_slice($strategyTable, 1));

            // 处理时间评估
            $this->newLine();
            $this->comment('⏱️  处理时间评估:');
            $hours = floor($strategy['estimated_processing_time_minutes'] / 60);
            $minutes = $strategy['estimated_processing_time_minutes'] % 60;
            
            if ($hours > 0) {
                $timeStr = "{$hours}小时{$minutes}分钟";
            } else {
                $timeStr = "{$minutes}分钟";
            }
            
            $this->line("预计完成时间: {$timeStr}");

            if ($strategy['estimated_processing_time_minutes'] > 480) { // 超过8小时
                $this->warn('⚠️  处理时间较长，建议考虑以下优化：');
                $this->line('  - 增加Worker进程数量');
                $this->line('  - 使用Redis队列替代数据库队列');
                $this->line('  - 优化数据源API性能');
                $this->line('  - 分批错峰处理');
            }

        } catch (\Exception $e) {
            $this->error('评估过程中出现错误: ' . $e->getMessage());
        }

        $this->newLine();
    }

    /**
     * 显示优化建议
     */
    private function displayRecommendations(): void
    {
        $this->info('💡 系统优化建议');

        $capacity = $this->bulkService->getSystemCapacity();
        $recommendations = [];

        // 基于当前状态生成建议
        $memoryUsage = $capacity['system_load']['memory_usage_percentage'] ?? 0;
        $queueSize = $capacity['queue_statistics']['total_pending'] ?? 0;
        $dailyCapacity = $capacity['estimated_daily_capacity'] ?? 0;

        if ($memoryUsage > 80) {
            $recommendations[] = [
                '内存优化',
                '当前内存使用率较高',
                '增加服务器内存或优化代码内存使用'
            ];
        }

        if ($queueSize > 100) {
            $recommendations[] = [
                '队列优化',
                '队列积压任务较多',
                '增加Worker进程数量或使用Redis队列'
            ];
        }

        if ($dailyCapacity < 300000) {
            $recommendations[] = [
                '处理能力提升',
                '日处理能力不足30万商品',
                '部署多台服务器或使用容器化部署'
            ];
        }

        // 通用优化建议
        $recommendations = array_merge($recommendations, [
            [
                'Redis队列',
                '提升队列处理性能',
                '配置Redis作为队列驱动，替代数据库队列'
            ],
            [
                'Supervisor进程监控',
                '确保Worker进程稳定运行',
                '使用Supervisor监控和自动重启Worker进程'
            ],
            [
                '数据库优化',
                '优化数据库查询性能',
                '添加索引、读写分离、分库分表'
            ],
            [
                'CDN和缓存',
                '减少重复请求和数据传输',
                '使用Redis缓存和CDN加速'
            ],
            [
                '监控和告警',
                '实时监控系统状态',
                '部署系统监控和自动告警机制'
            ],
        ]);

        if (!empty($recommendations)) {
            $this->table(['优化项目', '问题描述', '解决方案'], $recommendations);
        } else {
            $this->info('✅ 系统当前运行状态良好，暂无特殊优化建议');
        }

        $this->newLine();
        $this->comment('📚 详细优化指南:');
        $this->line('1. 队列扩容: php artisan queue:work --queue=bulk_processing --processes=8');
        $this->line('2. Redis配置: 修改 .env 中 QUEUE_CONNECTION=redis');
        $this->line('3. 内存优化: 增加 PHP memory_limit 到 1G 或更高');
        $this->line('4. 监控命令: php artisan queue:monitor');
        $this->newLine();
    }

    /**
     * 获取队列状态
     */
    private function getQueueStatus(int $pending): string
    {
        if ($pending > 100) return '⚠️  积压';
        if ($pending > 10) return '🟡 繁忙';
        return '✅ 正常';
    }

    /**
     * 获取内存状态
     */
    private function getMemoryStatus(float $usage): string
    {
        if ($usage > 80) return '⚠️  高';
        if ($usage > 60) return '🟡 中等';
        return '✅ 正常';
    }

    /**
     * 获取Worker状态
     */
    private function getWorkerStatus(int $workers): string
    {
        if ($workers == 0) return '❌ 无';
        if ($workers < 2) return '🟡 不足';
        return '✅ 正常';
    }

    /**
     * 获取处理能力状态
     */
    private function getCapacityStatus(float $current, float $target): string
    {
        $percentage = ($current / $target) * 100;
        
        if ($percentage >= 100) return '✅ 充足';
        if ($percentage >= 70) return '🟡 基本满足';
        if ($percentage >= 30) return '⚠️  不足';
        return '❌ 严重不足';
    }
} 