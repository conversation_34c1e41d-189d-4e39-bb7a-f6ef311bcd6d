<?php

namespace App\Services;

use App\Jobs\ProcessDataSourceTask;
use App\Models\MonitoringTask;
use App\Models\ProductData;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class BulkProcessingService
{
    /**
     * 批量处理商品数据采集
     *
     * @param MonitoringTask $monitoringTask
     * @param array $products 商品列表
     * @param int $batchSize 每批处理数量
     * @return string 批次ID
     */
    public function processBulkCollection(MonitoringTask $monitoringTask, array $products, int $batchSize = 100): string
    {
        $totalProducts = count($products);
        
        Log::info('开始大规模批量处理', [
            'monitoring_task_id' => $monitoringTask->id,
            'total_products' => $totalProducts,
            'batch_size' => $batchSize,
            'estimated_batches' => ceil($totalProducts / $batchSize),
        ]);

        // 将商品分片处理
        $chunks = array_chunk($products, $batchSize);
        $jobs = [];

        foreach ($chunks as $index => $chunk) {
            $jobs[] = new ProcessDataSourceTask(
                $monitoringTask->data_source_id,
                $chunk,
                ['batch_index' => $index, 'chunk_size' => count($chunk)],
                $monitoringTask->id
            );
        }

        // 使用Laravel Batch批量处理
        $batch = Bus::batch($jobs)
            ->then(function (Batch $batch) use ($monitoringTask, $totalProducts) {
                $this->onBatchCompleted($batch, $monitoringTask, $totalProducts);
            })
            ->catch(function (Batch $batch, \Throwable $e) use ($monitoringTask) {
                $this->onBatchFailed($batch, $monitoringTask, $e);
            })
            ->finally(function (Batch $batch) use ($monitoringTask) {
                $this->onBatchFinished($batch, $monitoringTask);
            })
            ->onQueue('bulk_processing')
            ->allowFailures()
            ->name("bulk_collection_{$monitoringTask->id}_" . now()->format('YmdHis'))
            ->dispatch();

        // 缓存批次信息
        $this->cacheBatchInfo($batch->id, $monitoringTask, $totalProducts);

        return $batch->id;
    }

    /**
     * 获取批量处理进度
     *
     * @param string $batchId
     * @return array
     */
    public function getBatchProgress(string $batchId): array
    {
        $batch = Bus::findBatch($batchId);
        
        if (!$batch) {
            return ['error' => '批次不存在'];
        }

        $cached = Cache::get("batch_info_{$batchId}", []);

        return [
            'batch_id' => $batchId,
            'name' => $batch->name,
            'total_jobs' => $batch->totalJobs,
            'pending_jobs' => $batch->pendingJobs,
            'processed_jobs' => $batch->processedJobs(),
            'failed_jobs' => $batch->failedJobs,
            'progress_percentage' => $batch->progress(),
            'finished_at' => $batch->finishedAt,
            'cancelled_at' => $batch->cancelledAt,
            'created_at' => $batch->createdAt,
            'estimated_completion' => $this->estimateCompletion($batch),
            'products_total' => $cached['products_total'] ?? 0,
            'monitoring_task_id' => $cached['monitoring_task_id'] ?? null,
        ];
    }

    /**
     * 获取系统处理能力评估
     *
     * @return array
     */
    public function getSystemCapacity(): array
    {
        $queueStats = $this->getQueueStatistics();
        $systemLoad = $this->getSystemLoad();
        
        // 基于当前性能评估最大处理能力
        $estimatedCapacity = $this->calculateCapacity($queueStats, $systemLoad);

        return [
            'queue_statistics' => $queueStats,
            'system_load' => $systemLoad,
            'estimated_daily_capacity' => $estimatedCapacity,
            'recommended_batch_size' => $this->getRecommendedBatchSize(),
            'optimal_worker_count' => $this->getOptimalWorkerCount(),
            'peak_hours_handling' => $this->getPeakHoursStrategy(),
        ];
    }

    /**
     * 智能分批策略
     *
     * @param int $totalProducts
     * @param MonitoringTask $monitoringTask
     * @return array
     */
    public function getOptimalBatchStrategy(int $totalProducts, MonitoringTask $monitoringTask): array
    {
        $systemCapacity = $this->getSystemCapacity();
        $dataSourcePerformance = $this->getDataSourcePerformance($monitoringTask->data_source_id);
        
        // 根据数据源性能调整批次大小
        $baseBatchSize = $systemCapacity['recommended_batch_size'];
        $adjustedBatchSize = $this->adjustBatchSizeForDataSource($baseBatchSize, $dataSourcePerformance);
        
        $estimatedBatches = ceil($totalProducts / $adjustedBatchSize);
        $estimatedTimeMinutes = $this->estimateProcessingTime($totalProducts, $dataSourcePerformance);
        
        return [
            'total_products' => $totalProducts,
            'recommended_batch_size' => $adjustedBatchSize,
            'estimated_batches' => $estimatedBatches,
            'estimated_processing_time_minutes' => $estimatedTimeMinutes,
            'recommended_worker_count' => min($estimatedBatches, $systemCapacity['optimal_worker_count']),
            'peak_memory_estimate_mb' => $this->estimateMemoryUsage($totalProducts),
            'should_use_queue_scaling' => $totalProducts > 10000,
        ];
    }

    /**
     * 批次完成回调
     */
    private function onBatchCompleted(Batch $batch, MonitoringTask $monitoringTask, int $totalProducts): void
    {
        Log::info('大规模批量处理完成', [
            'batch_id' => $batch->id,
            'monitoring_task_id' => $monitoringTask->id,
            'total_products' => $totalProducts,
            'total_jobs' => $batch->totalJobs,
            'processing_time_minutes' => $batch->createdAt->diffInMinutes($batch->finishedAt),
        ]);

        // 更新监控任务统计
        $monitoringTask->update([
            'last_run_at' => now(),
            'total_products' => $totalProducts,
            'success_count' => $monitoringTask->success_count + 1,
        ]);

        // 清理缓存
        Cache::forget("batch_info_{$batch->id}");
    }

    /**
     * 批次失败回调
     */
    private function onBatchFailed(Batch $batch, MonitoringTask $monitoringTask, \Throwable $e): void
    {
        Log::error('大规模批量处理失败', [
            'batch_id' => $batch->id,
            'monitoring_task_id' => $monitoringTask->id,
            'error' => $e->getMessage(),
            'failed_jobs' => $batch->failedJobs,
        ]);

        $monitoringTask->update([
            'failed_count' => $monitoringTask->failed_count + 1,
            'last_error' => $e->getMessage(),
        ]);
    }

    /**
     * 批次结束回调
     */
    private function onBatchFinished(Batch $batch, MonitoringTask $monitoringTask): void
    {
        Log::info('大规模批量处理结束', [
            'batch_id' => $batch->id,
            'monitoring_task_id' => $monitoringTask->id,
            'final_status' => $batch->finished() ? 'completed' : 'cancelled',
        ]);
    }

    /**
     * 缓存批次信息
     */
    private function cacheBatchInfo(string $batchId, MonitoringTask $monitoringTask, int $totalProducts): void
    {
        Cache::put("batch_info_{$batchId}", [
            'monitoring_task_id' => $monitoringTask->id,
            'products_total' => $totalProducts,
            'started_at' => now()->toISOString(),
        ], now()->addHours(24));
    }

    /**
     * 估算完成时间
     */
    private function estimateCompletion(Batch $batch): ?string
    {
        if ($batch->progress() == 0) {
            return null;
        }

        $elapsedMinutes = $batch->createdAt->diffInMinutes(now());
        $remainingMinutes = ($elapsedMinutes / $batch->progress()) * (100 - $batch->progress());
        
        return now()->addMinutes($remainingMinutes)->toISOString();
    }

    /**
     * 获取队列统计
     */
    private function getQueueStatistics(): array
    {
        try {
            $defaultQueue = DB::table('jobs')->where('queue', 'default')->count();
            $bulkQueue = DB::table('jobs')->where('queue', 'bulk_processing')->count();
            $alertsQueue = DB::table('jobs')->where('queue', 'alerts')->count();
            
            return [
                'default_queue_size' => $defaultQueue,
                'bulk_queue_size' => $bulkQueue,
                'alerts_queue_size' => $alertsQueue,
                'total_pending' => $defaultQueue + $bulkQueue + $alertsQueue,
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * 获取系统负载
     */
    private function getSystemLoad(): array
    {
        // 简化的系统负载检测
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        return [
            'memory_usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'memory_limit_mb' => round($memoryLimit / 1024 / 1024, 2),
            'memory_usage_percentage' => round(($memoryUsage / $memoryLimit) * 100, 2),
            'active_workers' => $this->getActiveWorkerCount(),
        ];
    }

    /**
     * 计算系统容量
     */
    private function calculateCapacity(array $queueStats, array $systemLoad): int
    {
        // 基础处理能力：每分钟100个商品
        $baseCapacity = 100;
        
        // 根据内存使用率调整
        $memoryFactor = max(0.1, 1 - ($systemLoad['memory_usage_percentage'] / 100));
        
        // 根据队列积压调整
        $queueFactor = max(0.5, 1 - ($queueStats['total_pending'] / 1000));
        
        $dailyCapacity = $baseCapacity * 60 * 24 * $memoryFactor * $queueFactor;
        
        return (int) round($dailyCapacity);
    }

    /**
     * 获取推荐批次大小
     */
    private function getRecommendedBatchSize(): int
    {
        $systemLoad = $this->getSystemLoad();
        
        if ($systemLoad['memory_usage_percentage'] > 80) {
            return 50; // 高负载时减小批次
        } elseif ($systemLoad['memory_usage_percentage'] < 30) {
            return 200; // 低负载时增大批次
        } else {
            return 100; // 默认批次大小
        }
    }

    /**
     * 获取最优worker数量
     */
    private function getOptimalWorkerCount(): int
    {
        $cpuCores = $this->getCpuCoreCount();
        return min(8, max(2, $cpuCores * 2)); // worker数量不超过8个
    }

    /**
     * 获取峰值处理策略
     */
    private function getPeakHoursStrategy(): array
    {
        return [
            'enable_auto_scaling' => true,
            'max_workers_peak' => 12,
            'queue_priority' => ['alerts', 'bulk_processing', 'default'],
            'batch_size_adjustment' => 'increase_by_50_percent',
            'memory_limit_increase' => '512M',
        ];
    }

    /**
     * 获取数据源性能指标
     */
    private function getDataSourcePerformance(int $dataSourceId): array
    {
        // 从缓存或数据库获取数据源性能统计
        return Cache::remember("data_source_performance_{$dataSourceId}", 3600, function () use ($dataSourceId) {
            // 计算平均响应时间和成功率
            $recentData = ProductData::where('data_source_id', $dataSourceId)
                ->where('created_at', '>=', now()->subDays(7))
                ->selectRaw('AVG(response_time) as avg_response_time, COUNT(*) as total_requests')
                ->first();

            return [
                'avg_response_time_ms' => $recentData->avg_response_time ?? 1000,
                'requests_per_minute_capacity' => 60, // 默认每分钟60个请求
                'success_rate' => 95, // 默认95%成功率
            ];
        });
    }

    /**
     * 根据数据源性能调整批次大小
     */
    private function adjustBatchSizeForDataSource(int $baseBatchSize, array $performance): int
    {
        $responseTime = $performance['avg_response_time_ms'];
        
        if ($responseTime > 5000) { // 响应时间超过5秒
            return (int) ($baseBatchSize * 0.5); // 减半
        } elseif ($responseTime < 1000) { // 响应时间低于1秒
            return (int) ($baseBatchSize * 1.5); // 增加50%
        }
        
        return $baseBatchSize;
    }

    /**
     * 估算处理时间
     */
    private function estimateProcessingTime(int $totalProducts, array $performance): int
    {
        $avgProcessingTimePerProduct = ($performance['avg_response_time_ms'] / 1000) + 2; // 加上2秒处理时间
        $totalTimeSeconds = $totalProducts * $avgProcessingTimePerProduct;
        
        return (int) ceil($totalTimeSeconds / 60); // 转换为分钟
    }

    /**
     * 估算内存使用
     */
    private function estimateMemoryUsage(int $totalProducts): int
    {
        // 每个商品数据大约占用10KB内存
        $memoryPerProduct = 10; // KB
        $totalMemoryKB = $totalProducts * $memoryPerProduct;
        
        return (int) ceil($totalMemoryKB / 1024); // 转换为MB
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit(string $memoryLimit): int
    {
        $memoryLimit = trim($memoryLimit);
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }

    /**
     * 获取活跃worker数量
     */
    private function getActiveWorkerCount(): int
    {
        // 简化实现，实际应该检查进程
        return 1;
    }

    /**
     * 获取CPU核心数
     */
    private function getCpuCoreCount(): int
    {
        if (function_exists('shell_exec')) {
            $cores = shell_exec('nproc');
            return $cores ? (int) trim($cores) : 4;
        }
        return 4; // 默认4核
    }
} 