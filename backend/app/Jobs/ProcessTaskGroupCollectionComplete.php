<?php

namespace App\Jobs;

use App\Services\AlertService;
use App\Services\TaskGroupAlertSummaryService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ProcessTaskGroupCollectionComplete implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 监控任务ID
     */
    private int $monitoringTaskId;

    /**
     * 采集批次ID
     */
    private string $collectionBatchId;

    /**
     * 任务执行超时时间（秒）
     */
    public int $timeout = 300;

    /**
     * 最大重试次数
     */
    public int $tries = 2;

    /**
     * 创建新的任务实例
     */
    public function __construct(int $monitoringTaskId, string $collectionBatchId)
    {
        $this->monitoringTaskId = $monitoringTaskId;
        $this->collectionBatchId = $collectionBatchId;
    }

    /**
     * 执行任务
     */
    public function handle(AlertService $alertService, TaskGroupAlertSummaryService $summaryService): void
    {
        Log::info('开始处理任务分组采集完成任务', [
            'monitoring_task_id' => $this->monitoringTaskId,
            'collection_batch_id' => $this->collectionBatchId,
        ]);

        try {
            // 使用Redis缓存确保同一个collection_batch_id不会被重复处理
            $processingKey = "task_group_completion_processing_{$this->collectionBatchId}";
            
            // 检查是否已经在处理这个批次
            if (Cache::has($processingKey)) {
                Log::info('任务分组采集完成任务已经在处理中，跳过重复处理', [
                    'monitoring_task_id' => $this->monitoringTaskId,
                    'collection_batch_id' => $this->collectionBatchId,
                ]);
                return;
            }

            // 设置处理中标记，防止重复处理（有效期30分钟）
            Cache::put($processingKey, true, now()->addMinutes(30));

            // 检查监控任务是否完成采集
            if (!$summaryService->isMonitoringTaskCollectionComplete($this->monitoringTaskId, $this->collectionBatchId)) {
                Log::info('监控任务尚未完成采集，跳过汇总处理', [
                    'monitoring_task_id' => $this->monitoringTaskId,
                    'collection_batch_id' => $this->collectionBatchId,
                ]);
                
                // 清除处理标记，允许后续重试
                Cache::forget($processingKey);
                return;
            }

            // 检查是否所有相关的预警检查都已完成
            $this->waitForAlertCheckingComplete();

            // 完成任务分组采集并触发汇总通知
            $alertService->completeTaskGroupCollection($this->monitoringTaskId, $this->collectionBatchId);

            // 处理完成，设置完成标记
            $completedKey = "task_group_completion_completed_{$this->collectionBatchId}";
            Cache::put($completedKey, true, now()->addHours(24));

            Log::info('任务分组采集完成任务处理成功', [
                'monitoring_task_id' => $this->monitoringTaskId,
                'collection_batch_id' => $this->collectionBatchId,
            ]);

        } catch (\Exception $e) {
            Log::error('任务分组采集完成任务处理失败', [
                'monitoring_task_id' => $this->monitoringTaskId,
                'collection_batch_id' => $this->collectionBatchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 处理失败，清除处理标记，允许重试
            $processingKey = "task_group_completion_processing_{$this->collectionBatchId}";
            Cache::forget($processingKey);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 等待预警检查完成
     * 
     * 智能等待机制：检查预警任务完成状态，而不是固定等待30秒
     */
    private function waitForAlertCheckingComplete(): void
    {
        $maxWaitTime = 60; // 最大等待60秒
        $checkInterval = 2; // 每2秒检查一次
        $waited = 0;

        Log::info('开始智能等待预警检查任务完成', [
            'monitoring_task_id' => $this->monitoringTaskId,
            'collection_batch_id' => $this->collectionBatchId,
            'max_wait_time' => $maxWaitTime,
            'check_interval' => $checkInterval,
        ]);

        while ($waited < $maxWaitTime) {
            // 检查是否还有未完成的预警检查任务
            $pendingAlertsCount = \App\Models\Alert::where('monitoring_task_id', $this->monitoringTaskId)
                ->whereJsonContains('trigger_data->collection_batch_id', $this->collectionBatchId)
                ->where('status', 'pending')
                ->count();

            // 检查数据库中是否有对应批次的任务正在队列中
            $queuedJobsCount = DB::table('jobs')
                ->where('payload', 'like', '%ProcessAlertChecking%')
                ->where('payload', 'like', '%' . $this->collectionBatchId . '%')
                ->count();

            if ($pendingAlertsCount == 0 && $queuedJobsCount == 0) {
                Log::info('所有预警检查任务已完成', [
                    'monitoring_task_id' => $this->monitoringTaskId,
                    'collection_batch_id' => $this->collectionBatchId,
                    'waited_seconds' => $waited,
                ]);
                return;
            }

            Log::debug('预警检查任务仍在进行中', [
                'monitoring_task_id' => $this->monitoringTaskId,
                'collection_batch_id' => $this->collectionBatchId,
                'pending_alerts_count' => $pendingAlertsCount,
                'queued_jobs_count' => $queuedJobsCount,
                'waited_seconds' => $waited,
            ]);

            sleep($checkInterval);
            $waited += $checkInterval;
        }

        Log::warning('预警检查等待超时，继续执行汇总', [
            'monitoring_task_id' => $this->monitoringTaskId,
            'collection_batch_id' => $this->collectionBatchId,
            'max_wait_time' => $maxWaitTime,
        ]);
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('任务分组采集完成任务最终失败', [
            'monitoring_task_id' => $this->monitoringTaskId,
            'collection_batch_id' => $this->collectionBatchId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }

    /**
     * 获取任务的唯一标识符
     */
    public function uniqueId(): string
    {
        return "task_group_collection_complete_{$this->monitoringTaskId}_{$this->collectionBatchId}";
    }

    /**
     * 获取任务的标签
     */
    public function tags(): array
    {
        return [
            'task_group_collection',
            'monitoring_task:' . $this->monitoringTaskId,
            'batch:' . $this->collectionBatchId,
        ];
    }
}
