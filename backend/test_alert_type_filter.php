<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\ProductDataController;
use App\Models\ProductData;
use App\Models\Alert;
use App\Models\AlertRule;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试预警类型筛选功能 ===\n\n";

// 1. 检查现有数据
echo "1. 检查现有数据...\n";
$totalAlerts = Alert::count();
$totalRules = AlertRule::count();
$totalProducts = ProductData::count();

echo "   总预警数: {$totalAlerts}\n";
echo "   总预警规则数: {$totalRules}\n";
echo "   总产品数: {$totalProducts}\n\n";

// 2. 检查预警规则类型分布
echo "2. 检查预警规则类型分布...\n";
$rules = AlertRule::all();
$ruleTypeStats = [];

foreach ($rules as $rule) {
    $ruleTypes = $rule->rule_type;
    if (is_array($ruleTypes)) {
        foreach ($ruleTypes as $type) {
            $ruleTypeStats[$type] = ($ruleTypeStats[$type] ?? 0) + 1;
        }
    }
}

foreach ($ruleTypeStats as $type => $count) {
    echo "   {$type}: {$count} 个规则\n";
}
echo "\n";

// 3. 测试预警类型筛选
echo "3. 测试预警类型筛选...\n";

$testTypes = ['promotion_price_deviation', 'channel_price_deviation', 'listing_status_change'];

foreach ($testTypes as $alertType) {
    echo "   测试筛选类型: {$alertType}\n";
    
    // 模拟请求
    $request = new Request();
    $request->merge([
        'alert_type' => $alertType,
        'per_page' => 10
    ]);
    
    // 创建控制器实例
    $controller = new ProductDataController();
    
    try {
        // 调用控制器方法
        $response = $controller->index($request);
        $responseData = $response->getData(true);
        
        if ($responseData['success']) {
            $products = $responseData['data']['data'] ?? [];
            echo "     找到 " . count($products) . " 个产品\n";
            
            // 验证结果
            foreach ($products as $product) {
                if (isset($product['alerts']) && !empty($product['alerts'])) {
                    foreach ($product['alerts'] as $alert) {
                        echo "       产品 {$product['item_id']} 有预警: {$alert['title']}\n";
                    }
                }
            }
        } else {
            echo "     筛选失败: " . ($responseData['message'] ?? '未知错误') . "\n";
        }
    } catch (\Exception $e) {
        echo "     筛选出错: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 4. 直接查询数据库验证
echo "4. 直接查询数据库验证...\n";

foreach ($testTypes as $alertType) {
    echo "   查询预警类型: {$alertType}\n";
    
    // 查询包含该预警类型的预警规则
    $rulesWithType = AlertRule::whereJsonContains('rule_type', $alertType)->count();
    echo "     包含该类型的预警规则数: {$rulesWithType}\n";
    
    // 查询相关的预警
    $alertsWithType = Alert::whereHas('alertRule', function ($q) use ($alertType) {
        $q->whereJsonContains('rule_type', $alertType);
    })->count();
    echo "     该类型的预警数: {$alertsWithType}\n";
    
    // 查询有该类型预警的产品
    $productsWithAlerts = ProductData::whereHas('alerts', function ($q) use ($alertType) {
        $q->where('status', '!=', 'resolved')
          ->whereHas('alertRule', function ($subQ) use ($alertType) {
              $subQ->whereJsonContains('rule_type', $alertType);
          });
    })->count();
    echo "     有该类型预警的产品数: {$productsWithAlerts}\n\n";
}

echo "=== 测试完成 ===\n";
